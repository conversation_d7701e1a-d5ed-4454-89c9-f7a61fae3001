package ru.regionprivates.managers;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import ru.regionprivates.RegionPrivatesPlugin;
import ru.regionprivates.models.PrivateRegion;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

public class PrivateManager {
    
    private final RegionPrivatesPlugin plugin;
    private final Map<String, PrivateRegion> regions;
    private final Map<Location, String> blockToRegion;
    
    // Материалы, которые можно использовать для создания приватов
    private final Set<Material> allowedBlocks = Set.of(
        Material.DIAMOND_BLOCK,
        Material.EMERALD_BLOCK,
        Material.GOLD_BLOCK,
        Material.IRON_BLOCK,
        Material.BEACON
    );
    
    public PrivateManager(RegionPrivatesPlugin plugin) {
        this.plugin = plugin;
        this.regions = new ConcurrentHashMap<>();
        this.blockToRegion = new ConcurrentHashMap<>();
    }
    
    /**
     * Создает новый приват в указанной локации
     */
    public boolean createPrivate(Player owner, Location blockLocation) {
        // Проверяем, что блок подходящего типа
        Material blockType = blockLocation.getBlock().getType();
        if (!allowedBlocks.contains(blockType)) {
            return false;
        }
        
        // Проверяем, что в этой локации еще нет привата
        if (blockToRegion.containsKey(blockLocation)) {
            return false;
        }
        
        // Проверяем, что поблизости нет других приватов (минимум 32 блока)
        for (PrivateRegion region : regions.values()) {
            if (region.getWorld().equals(blockLocation.getWorld()) &&
                region.getBlockLocation().distance(blockLocation) < 32.0) {
                return false;
            }
        }
        
        // Создаем новый приват
        String regionId = generateRegionId();
        PrivateRegion region = new PrivateRegion(regionId, owner.getUniqueId(), blockLocation);
        
        regions.put(regionId, region);
        blockToRegion.put(blockLocation, regionId);
        
        return true;
    }
    
    /**
     * Удаляет приват
     */
    public boolean removePrivate(String regionId, UUID requesterId) {
        PrivateRegion region = regions.get(regionId);
        if (region == null) {
            return false;
        }
        
        // Проверяем права на удаление
        if (!region.getOwner().equals(requesterId) && 
            !plugin.getServer().getPlayer(requesterId).hasPermission("regionprivates.admin")) {
            return false;
        }
        
        regions.remove(regionId);
        blockToRegion.remove(region.getBlockLocation());
        
        return true;
    }
    
    /**
     * Находит приват по локации блока
     */
    public PrivateRegion getPrivateByBlock(Location blockLocation) {
        String regionId = blockToRegion.get(blockLocation);
        return regionId != null ? regions.get(regionId) : null;
    }
    
    /**
     * Находит приват, в зоне которого находится указанная локация
     */
    public PrivateRegion getPrivateByLocation(Location location) {
        for (PrivateRegion region : regions.values()) {
            if (region.isInRegion(location)) {
                return region;
            }
        }
        return null;
    }
    
    /**
     * Получает все приваты игрока
     */
    public List<PrivateRegion> getPlayerPrivates(UUID playerId) {
        return regions.values().stream()
                .filter(region -> region.getOwner().equals(playerId))
                .collect(Collectors.toList());
    }
    
    /**
     * Добавляет игрока в приват
     */
    public boolean addPlayerToPrivate(String regionId, UUID playerId, UUID requesterId) {
        PrivateRegion region = regions.get(regionId);
        if (region == null) {
            return false;
        }
        
        // Проверяем права на добавление
        if (!region.getOwner().equals(requesterId)) {
            return false;
        }
        
        return region.addMember(playerId);
    }
    
    /**
     * Удаляет игрока из привата
     */
    public boolean removePlayerFromPrivate(String regionId, UUID playerId, UUID requesterId) {
        PrivateRegion region = regions.get(regionId);
        if (region == null) {
            return false;
        }
        
        // Проверяем права на удаление
        if (!region.getOwner().equals(requesterId)) {
            return false;
        }
        
        return region.removeMember(playerId);
    }
    
    /**
     * Переключает PvP в привате
     */
    public boolean togglePvP(String regionId, UUID requesterId) {
        PrivateRegion region = regions.get(regionId);
        if (region == null) {
            return false;
        }
        
        // Проверяем права на изменение PvP
        if (!region.getOwner().equals(requesterId)) {
            return false;
        }
        
        region.togglePvP();
        return true;
    }
    
    /**
     * Проверяет, может ли игрок взаимодействовать с блоком
     */
    public boolean canInteract(UUID playerId, Location location) {
        PrivateRegion region = getPrivateByLocation(location);
        if (region == null) {
            return true; // Нет привата - можно взаимодействовать
        }
        
        return region.hasAccess(playerId);
    }
    
    /**
     * Проверяет, разрешен ли PvP в данной локации
     */
    public boolean isPvPAllowed(Location location) {
        PrivateRegion region = getPrivateByLocation(location);
        if (region == null) {
            return true; // Нет привата - PvP разрешен
        }
        
        return region.isPvpEnabled();
    }
    
    /**
     * Генерирует уникальный ID для региона
     */
    private String generateRegionId() {
        return "region_" + System.currentTimeMillis() + "_" + 
               (int)(Math.random() * 1000);
    }
    
    /**
     * Получает все регионы (для сохранения)
     */
    public Map<String, PrivateRegion> getAllRegions() {
        return new HashMap<>(regions);
    }
    
    /**
     * Загружает регионы из данных
     */
    public void loadRegions(Map<String, PrivateRegion> loadedRegions) {
        regions.clear();
        blockToRegion.clear();
        
        for (Map.Entry<String, PrivateRegion> entry : loadedRegions.entrySet()) {
            regions.put(entry.getKey(), entry.getValue());
            blockToRegion.put(entry.getValue().getBlockLocation(), entry.getKey());
        }
    }
    
    /**
     * Получает разрешенные блоки для создания приватов
     */
    public Set<Material> getAllowedBlocks() {
        return new HashSet<>(allowedBlocks);
    }
}
