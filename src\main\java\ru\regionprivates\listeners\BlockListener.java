package ru.regionprivates.listeners;

import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.*;
import org.bukkit.event.entity.EntityExplodeEvent;
import ru.regionprivates.RegionPrivatesPlugin;
import ru.regionprivates.models.PrivateRegion;

public class BlockListener implements Listener {
    
    private final RegionPrivatesPlugin plugin;
    
    public BlockListener(RegionPrivatesPlugin plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler(priority = EventPriority.HIGH)
    public void onBlockPlace(BlockPlaceEvent event) {
        Player player = event.getPlayer();
        Block block = event.getBlock();
        Material blockType = block.getType();
        
        // Проверяем, является ли блок подходящим для создания привата
        if (plugin.getPrivateManager().getAllowedBlocks().contains(blockType)) {
            // Проверяем разрешения
            if (!player.hasPermission("regionprivates.create")) {
                player.sendMessage(ChatColor.RED + "У вас нет прав на создание приватов!");
                return;
            }
            
            // Пытаемся создать приват
            if (plugin.getPrivateManager().createPrivate(player, block.getLocation())) {
                player.sendMessage(ChatColor.GREEN + "Приват успешно создан!");
                player.sendMessage(ChatColor.YELLOW + "Зона защиты: 16 блоков во все стороны от блока");
                player.sendMessage(ChatColor.YELLOW + "Используйте /private для управления приватом");
                
                // Сохраняем данные
                plugin.getDataManager().saveData();
            } else {
                player.sendMessage(ChatColor.RED + "Не удалось создать приват!");
                player.sendMessage(ChatColor.RED + "Возможные причины:");
                player.sendMessage(ChatColor.RED + "- Слишком близко к другому привату (минимум 32 блока)");
                player.sendMessage(ChatColor.RED + "- В этом месте уже есть приват");
            }
        }
    }
    
    @EventHandler(priority = EventPriority.HIGH)
    public void onBlockBreak(BlockBreakEvent event) {
        Player player = event.getPlayer();
        Block block = event.getBlock();
        Location blockLocation = block.getLocation();
        
        // Проверяем, является ли этот блок блоком привата
        PrivateRegion privateByBlock = plugin.getPrivateManager().getPrivateByBlock(blockLocation);
        if (privateByBlock != null) {
            // Это блок привата - проверяем права на удаление
            if (!privateByBlock.getOwner().equals(player.getUniqueId()) && 
                !player.hasPermission("regionprivates.admin")) {
                event.setCancelled(true);
                player.sendMessage(ChatColor.RED + "Вы не можете сломать чужой приват!");
                return;
            }
            
            // Удаляем приват
            if (plugin.getPrivateManager().removePrivate(privateByBlock.getId(), player.getUniqueId())) {
                player.sendMessage(ChatColor.YELLOW + "Приват удален!");
                plugin.getDataManager().saveData();
            }
            return;
        }
        
        // Проверяем, находится ли блок в зоне чужого привата
        PrivateRegion region = plugin.getPrivateManager().getPrivateByLocation(blockLocation);
        if (region != null) {
            // Проверяем права доступа
            if (!plugin.getPrivateManager().canInteract(player.getUniqueId(), blockLocation) &&
                !player.hasPermission("regionprivates.bypass")) {
                event.setCancelled(true);
                player.sendMessage(ChatColor.RED + "Вы не можете ломать блоки в чужом привате!");
                
                // Показываем информацию о владельце
                String ownerName = plugin.getServer().getOfflinePlayer(region.getOwner()).getName();
                if (ownerName != null) {
                    player.sendMessage(ChatColor.RED + "Владелец привата: " + ownerName);
                }
            }
        }
    }

    @EventHandler(priority = EventPriority.HIGH)
    public void onBlockBurn(BlockBurnEvent event) {
        // Защита от огня
        PrivateRegion region = plugin.getPrivateManager().getPrivateByLocation(event.getBlock().getLocation());
        if (region != null) {
            event.setCancelled(true);
        }
    }

    @EventHandler(priority = EventPriority.HIGH)
    public void onBlockSpread(BlockSpreadEvent event) {
        // Защита от распространения блоков (огонь, лава и т.д.)
        PrivateRegion region = plugin.getPrivateManager().getPrivateByLocation(event.getBlock().getLocation());
        if (region != null) {
            event.setCancelled(true);
        }
    }

    @EventHandler(priority = EventPriority.HIGH)
    public void onBlockFromTo(BlockFromToEvent event) {
        // Защита от потоков воды/лавы
        PrivateRegion region = plugin.getPrivateManager().getPrivateByLocation(event.getToBlock().getLocation());
        if (region != null) {
            event.setCancelled(true);
        }
    }

    @EventHandler(priority = EventPriority.HIGH)
    public void onBlockPistonExtend(BlockPistonExtendEvent event) {
        // Защита от поршней
        for (Block block : event.getBlocks()) {
            PrivateRegion region = plugin.getPrivateManager().getPrivateByLocation(block.getLocation());
            if (region != null) {
                event.setCancelled(true);
                return;
            }
        }
    }

    @EventHandler(priority = EventPriority.HIGH)
    public void onBlockPistonRetract(BlockPistonRetractEvent event) {
        // Защита от поршней (втягивание)
        for (Block block : event.getBlocks()) {
            PrivateRegion region = plugin.getPrivateManager().getPrivateByLocation(block.getLocation());
            if (region != null) {
                event.setCancelled(true);
                return;
            }
        }
    }
}
