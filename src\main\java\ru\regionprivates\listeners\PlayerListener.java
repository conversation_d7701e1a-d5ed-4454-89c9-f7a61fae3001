package ru.regionprivates.listeners;

import org.bukkit.ChatColor;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Monster;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.CreatureSpawnEvent;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityExplodeEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import ru.regionprivates.RegionPrivatesPlugin;
import ru.regionprivates.models.PrivateRegion;

public class PlayerListener implements Listener {
    
    private final RegionPrivatesPlugin plugin;
    
    public PlayerListener(RegionPrivatesPlugin plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler(priority = EventPriority.HIGH)
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        
        // Проверяем только взаимодействие с блоками
        if (event.getClickedBlock() == null) {
            return;
        }
        
        // Проверяем, находится ли блок в зоне привата
        PrivateRegion region = plugin.getPrivateManager().getPrivateByLocation(event.getClickedBlock().getLocation());
        if (region != null) {
            // Проверяем права доступа
            if (!plugin.getPrivateManager().canInteract(player.getUniqueId(), event.getClickedBlock().getLocation()) &&
                !player.hasPermission("regionprivates.bypass")) {
                event.setCancelled(true);
                player.sendMessage(ChatColor.RED + "Вы не можете взаимодействовать с блоками в чужом привате!");
                
                // Показываем информацию о владельце
                String ownerName = plugin.getServer().getOfflinePlayer(region.getOwner()).getName();
                if (ownerName != null) {
                    player.sendMessage(ChatColor.RED + "Владелец привата: " + ownerName);
                }
            }
        }
    }
    
    @EventHandler(priority = EventPriority.HIGH)
    public void onEntityDamageByEntity(EntityDamageByEntityEvent event) {
        // Проверяем PvP между игроками
        if (!(event.getEntity() instanceof Player) || !(event.getDamager() instanceof Player)) {
            return;
        }
        
        Player victim = (Player) event.getEntity();
        Player attacker = (Player) event.getDamager();
        
        // Проверяем, находятся ли игроки в привате
        PrivateRegion region = plugin.getPrivateManager().getPrivateByLocation(victim.getLocation());
        if (region != null) {
            // Проверяем настройки PvP в привате
            if (!plugin.getPrivateManager().isPvPAllowed(victim.getLocation())) {
                event.setCancelled(true);
                attacker.sendMessage(ChatColor.RED + "PvP запрещен в этом привате!");
                
                // Показываем информацию о владельце
                String ownerName = plugin.getServer().getOfflinePlayer(region.getOwner()).getName();
                if (ownerName != null) {
                    attacker.sendMessage(ChatColor.RED + "Владелец привата: " + ownerName);
                }
            }
        }
    }

    @EventHandler(priority = EventPriority.HIGH)
    public void onEntityExplode(EntityExplodeEvent event) {
        // Защита от взрывов
        event.blockList().removeIf(block -> {
            PrivateRegion region = plugin.getPrivateManager().getPrivateByLocation(block.getLocation());
            return region != null;
        });
    }

    @EventHandler(priority = EventPriority.NORMAL)
    public void onCreatureSpawn(CreatureSpawnEvent event) {
        // Предотвращаем спавн враждебных мобов в приватах
        if (event.getEntity() instanceof Monster) {
            PrivateRegion region = plugin.getPrivateManager().getPrivateByLocation(event.getLocation());
            if (region != null) {
                event.setCancelled(true);
            }
        }
    }
}
