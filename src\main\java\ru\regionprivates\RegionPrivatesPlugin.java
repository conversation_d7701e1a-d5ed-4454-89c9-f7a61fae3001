package ru.regionprivates;

import org.bukkit.plugin.java.JavaPlugin;
import ru.regionprivates.commands.PrivateCommand;
import ru.regionprivates.listeners.BlockListener;
import ru.regionprivates.listeners.PlayerListener;
import ru.regionprivates.managers.PrivateManager;
import ru.regionprivates.storage.DataManager;

public class RegionPrivatesPlugin extends JavaPlugin {
    
    private PrivateManager privateManager;
    private DataManager dataManager;
    
    @Override
    public void onEnable() {
        // Создание директории для данных
        if (!getDataFolder().exists()) {
            getDataFolder().mkdirs();
        }
        
        // Инициализация менеджеров
        this.dataManager = new DataManager(this);
        this.privateManager = new PrivateManager(this);
        
        // Загрузка данных
        dataManager.loadData();
        
        // Регистрация команд
        getCommand("private").setExecutor(new PrivateCommand(this));
        
        // Регистрация слушателей событий
        getServer().getPluginManager().registerEvents(new BlockListener(this), this);
        getServer().getPluginManager().registerEvents(new PlayerListener(this), this);
        
        getLogger().info("RegionPrivates плагин успешно загружен!");
    }
    
    @Override
    public void onDisable() {
        // Сохранение данных при выключении
        if (dataManager != null) {
            dataManager.saveData();
        }
        
        getLogger().info("RegionPrivates плагин выключен!");
    }
    
    public PrivateManager getPrivateManager() {
        return privateManager;
    }
    
    public DataManager getDataManager() {
        return dataManager;
    }
}
