package ru.regionprivates.storage;

import com.google.gson.*;
import com.google.gson.reflect.TypeToken;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import ru.regionprivates.RegionPrivatesPlugin;
import ru.regionprivates.models.PrivateRegion;

import java.io.*;
import java.lang.reflect.Type;
import java.util.*;

public class DataManager {
    
    private final RegionPrivatesPlugin plugin;
    private final File dataFile;
    private final Gson gson;
    
    public DataManager(RegionPrivatesPlugin plugin) {
        this.plugin = plugin;
        this.dataFile = new File(plugin.getDataFolder(), "privates.json");
        
        // Настраиваем Gson с кастомными адаптерами
        this.gson = new GsonBuilder()
                .registerTypeAdapter(Location.class, new LocationAdapter())
                .registerTypeAdapter(Material.class, new MaterialAdapter())
                .registerTypeAdapter(UUID.class, new UUIDAdapter())
                .setPrettyPrinting()
                .create();
    }
    
    /**
     * Сохраняет данные приватов в файл
     */
    public void saveData() {
        try {
            // Создаем директорию если её нет
            if (!plugin.getDataFolder().exists()) {
                plugin.getDataFolder().mkdirs();
            }
            
            // Получаем все регионы
            Map<String, PrivateRegion> regions = plugin.getPrivateManager().getAllRegions();
            
            // Конвертируем в JSON и сохраняем
            try (FileWriter writer = new FileWriter(dataFile)) {
                gson.toJson(regions, writer);
            }
            
            plugin.getLogger().info("Данные приватов сохранены (" + regions.size() + " регионов)");
            
        } catch (IOException e) {
            plugin.getLogger().severe("Ошибка при сохранении данных: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Загружает данные приватов из файла
     */
    public void loadData() {
        if (!dataFile.exists()) {
            plugin.getLogger().info("Файл данных не найден, создается новый");
            return;
        }
        
        try {
            // Читаем файл
            try (FileReader reader = new FileReader(dataFile)) {
                Type type = new TypeToken<Map<String, PrivateRegion>>(){}.getType();
                Map<String, PrivateRegion> regions = gson.fromJson(reader, type);
                
                if (regions == null) {
                    regions = new HashMap<>();
                }
                
                // Фильтруем регионы с недоступными мирами
                Map<String, PrivateRegion> validRegions = new HashMap<>();
                for (Map.Entry<String, PrivateRegion> entry : regions.entrySet()) {
                    PrivateRegion region = entry.getValue();
                    if (region.getWorld() != null) {
                        validRegions.put(entry.getKey(), region);
                    } else {
                        plugin.getLogger().warning("Пропущен регион " + entry.getKey() + 
                                                 " - мир недоступен");
                    }
                }
                
                // Загружаем в менеджер
                plugin.getPrivateManager().loadRegions(validRegions);
                
                plugin.getLogger().info("Загружено " + validRegions.size() + " приватов");
            }
            
        } catch (IOException | JsonSyntaxException e) {
            plugin.getLogger().severe("Ошибка при загрузке данных: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Адаптер для сериализации Location
     */
    private static class LocationAdapter implements JsonSerializer<Location>, JsonDeserializer<Location> {
        @Override
        public JsonElement serialize(Location location, Type type, JsonSerializationContext context) {
            JsonObject obj = new JsonObject();
            obj.addProperty("world", location.getWorld().getName());
            obj.addProperty("x", location.getX());
            obj.addProperty("y", location.getY());
            obj.addProperty("z", location.getZ());
            obj.addProperty("yaw", location.getYaw());
            obj.addProperty("pitch", location.getPitch());
            return obj;
        }
        
        @Override
        public Location deserialize(JsonElement json, Type type, JsonDeserializationContext context) 
                throws JsonParseException {
            JsonObject obj = json.getAsJsonObject();
            
            String worldName = obj.get("world").getAsString();
            World world = Bukkit.getWorld(worldName);
            
            if (world == null) {
                return null; // Мир недоступен
            }
            
            double x = obj.get("x").getAsDouble();
            double y = obj.get("y").getAsDouble();
            double z = obj.get("z").getAsDouble();
            float yaw = obj.has("yaw") ? obj.get("yaw").getAsFloat() : 0;
            float pitch = obj.has("pitch") ? obj.get("pitch").getAsFloat() : 0;
            
            return new Location(world, x, y, z, yaw, pitch);
        }
    }
    
    /**
     * Адаптер для сериализации Material
     */
    private static class MaterialAdapter implements JsonSerializer<Material>, JsonDeserializer<Material> {
        @Override
        public JsonElement serialize(Material material, Type type, JsonSerializationContext context) {
            return new JsonPrimitive(material.name());
        }
        
        @Override
        public Material deserialize(JsonElement json, Type type, JsonDeserializationContext context) 
                throws JsonParseException {
            try {
                return Material.valueOf(json.getAsString());
            } catch (IllegalArgumentException e) {
                return Material.STONE; // Fallback материал
            }
        }
    }
    
    /**
     * Адаптер для сериализации UUID
     */
    private static class UUIDAdapter implements JsonSerializer<UUID>, JsonDeserializer<UUID> {
        @Override
        public JsonElement serialize(UUID uuid, Type type, JsonSerializationContext context) {
            return new JsonPrimitive(uuid.toString());
        }
        
        @Override
        public UUID deserialize(JsonElement json, Type type, JsonDeserializationContext context) 
                throws JsonParseException {
            return UUID.fromString(json.getAsString());
        }
    }
}
