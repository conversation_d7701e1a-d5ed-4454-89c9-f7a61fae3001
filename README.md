# RegionPrivates - Плагин приватов через блоки

Плагин для создания приватных территорий через установку специальных блоков в Minecraft.

## Возможности

- **Создание приватов через блоки**: Поставьте алмазный, изумрудный, золотой, железный блок или маяк для создания привата
- **Управление участниками**: Добавляйте и удаляйте игроков из своих приватов
- **Контроль PvP**: Включайте/выключайте PvP в каждом привате отдельно
- **Информация о приватах**: Просматривайте информацию о любом привате, находясь в нем
- **Список приватов**: Просматривайте все свои приваты с координатами и информацией о мире
- **Полная защита**: Защита от разрушения, взрывов, огня, потоков жидкости и враждебных мобов

## Команды

### Основные команды
- `/private` - Показать справку по командам
- `/private add <игрок>` - Добавить игрока в приват (только владелец)
- `/private remove <игрок>` - Удалить игрока из привата (только владелец)
- `/private pvp` - Переключить режим PvP в привате (только владелец)
- `/private info` - Показать информацию о текущем привате
- `/private list` - Показать список всех ваших приватов

### Алиасы
- `/priv` - Короткий алиас для `/private`
- `/pr` - Еще более короткий алиас

## Разрешения

- `regionprivates.use` - Основное разрешение для использования плагина (по умолчанию: true)
- `regionprivates.create` - Создание приватов (по умолчанию: true)
- `regionprivates.manage` - Управление своими приватами (по умолчанию: true)
- `regionprivates.admin` - Административные права (по умолчанию: op)
- `regionprivates.bypass` - Обход защиты приватов (по умолчанию: op)

## Как использовать

### Создание привата
1. Возьмите один из разрешенных блоков:
   - Алмазный блок
   - Изумрудный блок
   - Золотой блок
   - Железный блок
   - Маяк
2. Поставьте блок в нужном месте
3. Приват автоматически создастся с радиусом защиты 16 блоков во все стороны

### Управление приватом
1. Зайдите в зону своего привата
2. Используйте команды `/private add/remove/pvp` для управления
3. Команда `/private info` покажет всю информацию о привате

### Удаление привата
Просто сломайте блок-приват. Только владелец может сломать свой приват.

## Особенности защиты

- **Радиус защиты**: 16 блоков во все стороны от блока-привата
- **Минимальное расстояние**: Между приватами должно быть минимум 32 блока
- **Защита от**:
  - Разрушения блоков посторонними игроками
  - Взрывов (криперы, TNT, и т.д.)
  - Огня и распространения огня
  - Потоков воды и лавы
  - Работы поршней
  - Спавна враждебных мобов
- **PvP контроль**: Владелец может включать/выключать PvP в своем привате

## Установка

1. Скачайте jar файл плагина
2. Поместите его в папку `plugins` вашего сервера
3. Перезапустите сервер
4. Плагин создаст файл конфигурации `config.yml` в папке `plugins/RegionPrivates/`

## Конфигурация

Основные настройки в `config.yml`:
- `private.protection-radius` - Радиус защиты (по умолчанию: 16)
- `private.min-distance` - Минимальное расстояние между приватами (по умолчанию: 32)
- `private.max-privates-per-player` - Максимум приватов на игрока (по умолчанию: 5)
- `private.allowed-blocks` - Список разрешенных блоков для создания приватов

## Хранение данных

Данные приватов сохраняются в файле `plugins/RegionPrivates/privates.json` в формате JSON.
Данные автоматически сохраняются при:
- Создании нового привата
- Удалении привата
- Выключении сервера

## Требования

- Minecraft 1.20.1+
- Spigot/Paper сервер
- Java 17+

## Поддержка

При возникновении проблем проверьте:
1. Версию сервера (должна быть 1.20.1+)
2. Логи сервера на наличие ошибок
3. Правильность настроек в config.yml
4. Наличие необходимых разрешений у игроков
