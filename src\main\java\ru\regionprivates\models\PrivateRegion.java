package ru.regionprivates.models;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.block.Block;

import java.util.*;

public class PrivateRegion {
    
    private final String id;
    private final UUID owner;
    private final Location blockLocation;
    private final Set<UUID> members;
    private boolean pvpEnabled;
    private final long createdTime;
    private Material blockType;
    
    public PrivateRegion(String id, UUID owner, Location blockLocation) {
        this.id = id;
        this.owner = owner;
        this.blockLocation = blockLocation.clone();
        this.members = new HashSet<>();
        this.pvpEnabled = false;
        this.createdTime = System.currentTimeMillis();
        
        // Определяем тип блока
        Block block = blockLocation.getBlock();
        this.blockType = block.getType();
    }
    
    // Конструктор для загрузки из файла
    public PrivateRegion(String id, UUID owner, Location blockLocation, Set<UUID> members, 
                        boolean pvpEnabled, long createdTime, Material blockType) {
        this.id = id;
        this.owner = owner;
        this.blockLocation = blockLocation.clone();
        this.members = new HashSet<>(members);
        this.pvpEnabled = pvpEnabled;
        this.createdTime = createdTime;
        this.blockType = blockType;
    }
    
    /**
     * Проверяет, находится ли указанная локация в зоне действия привата
     * Зона действия - 16 блоков в каждую сторону от блока-привата
     */
    public boolean isInRegion(Location location) {
        if (!location.getWorld().equals(blockLocation.getWorld())) {
            return false;
        }
        
        double distance = location.distance(blockLocation);
        return distance <= 16.0;
    }
    
    /**
     * Проверяет, имеет ли игрок доступ к привату
     */
    public boolean hasAccess(UUID playerId) {
        return owner.equals(playerId) || members.contains(playerId);
    }
    
    /**
     * Добавляет игрока в приват
     */
    public boolean addMember(UUID playerId) {
        if (owner.equals(playerId)) {
            return false; // Владелец уже имеет доступ
        }
        return members.add(playerId);
    }
    
    /**
     * Удаляет игрока из привата
     */
    public boolean removeMember(UUID playerId) {
        return members.remove(playerId);
    }
    
    /**
     * Переключает режим PvP в привате
     */
    public void togglePvP() {
        this.pvpEnabled = !this.pvpEnabled;
    }
    
    // Геттеры
    public String getId() {
        return id;
    }
    
    public UUID getOwner() {
        return owner;
    }
    
    public Location getBlockLocation() {
        return blockLocation.clone();
    }
    
    public Set<UUID> getMembers() {
        return new HashSet<>(members);
    }
    
    public boolean isPvpEnabled() {
        return pvpEnabled;
    }
    
    public long getCreatedTime() {
        return createdTime;
    }
    
    public Material getBlockType() {
        return blockType;
    }
    
    public World getWorld() {
        return blockLocation.getWorld();
    }
    
    // Сеттеры
    public void setPvpEnabled(boolean pvpEnabled) {
        this.pvpEnabled = pvpEnabled;
    }
    
    public void setBlockType(Material blockType) {
        this.blockType = blockType;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        PrivateRegion that = (PrivateRegion) obj;
        return Objects.equals(id, that.id);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
    
    @Override
    public String toString() {
        return "PrivateRegion{" +
                "id='" + id + '\'' +
                ", owner=" + owner +
                ", location=" + blockLocation +
                ", members=" + members.size() +
                ", pvp=" + pvpEnabled +
                '}';
    }
}
