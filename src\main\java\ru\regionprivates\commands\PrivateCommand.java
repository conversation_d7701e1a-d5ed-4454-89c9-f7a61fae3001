package ru.regionprivates.commands;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import ru.regionprivates.RegionPrivatesPlugin;
import ru.regionprivates.models.PrivateRegion;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

public class PrivateCommand implements CommandExecutor {
    
    private final RegionPrivatesPlugin plugin;
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("dd.MM.yyyy HH:mm");
    
    public PrivateCommand(RegionPrivatesPlugin plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "Эта команда доступна только игрокам!");
            return true;
        }
        
        Player player = (Player) sender;
        
        if (args.length == 0) {
            sendHelp(player);
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "add":
                handleAddCommand(player, args);
                break;
            case "remove":
                handleRemoveCommand(player, args);
                break;
            case "pvp":
                handlePvPCommand(player);
                break;
            case "info":
                handleInfoCommand(player);
                break;
            case "list":
                handleListCommand(player);
                break;
            default:
                sendHelp(player);
                break;
        }
        
        return true;
    }
    
    private void handleAddCommand(Player player, String[] args) {
        if (args.length < 2) {
            player.sendMessage(ChatColor.RED + "Использование: /private add <игрок>");
            return;
        }
        
        // Находим приват в текущей локации
        PrivateRegion region = plugin.getPrivateManager().getPrivateByLocation(player.getLocation());
        if (region == null) {
            player.sendMessage(ChatColor.RED + "Вы не находитесь в привате!");
            return;
        }
        
        // Проверяем, что игрок - владелец
        if (!region.getOwner().equals(player.getUniqueId())) {
            player.sendMessage(ChatColor.RED + "Вы не являетесь владельцем этого привата!");
            return;
        }
        
        // Находим целевого игрока
        Player target = Bukkit.getPlayer(args[1]);
        if (target == null) {
            player.sendMessage(ChatColor.RED + "Игрок не найден!");
            return;
        }
        
        // Добавляем игрока
        if (plugin.getPrivateManager().addPlayerToPrivate(region.getId(), target.getUniqueId(), player.getUniqueId())) {
            player.sendMessage(ChatColor.GREEN + "Игрок " + target.getName() + " добавлен в приват!");
            target.sendMessage(ChatColor.GREEN + "Вы добавлены в приват игрока " + player.getName() + "!");
        } else {
            player.sendMessage(ChatColor.RED + "Не удалось добавить игрока в приват!");
        }
    }
    
    private void handleRemoveCommand(Player player, String[] args) {
        if (args.length < 2) {
            player.sendMessage(ChatColor.RED + "Использование: /private remove <игрок>");
            return;
        }
        
        // Находим приват в текущей локации
        PrivateRegion region = plugin.getPrivateManager().getPrivateByLocation(player.getLocation());
        if (region == null) {
            player.sendMessage(ChatColor.RED + "Вы не находитесь в привате!");
            return;
        }
        
        // Проверяем, что игрок - владелец
        if (!region.getOwner().equals(player.getUniqueId())) {
            player.sendMessage(ChatColor.RED + "Вы не являетесь владельцем этого привата!");
            return;
        }
        
        // Находим целевого игрока
        Player target = Bukkit.getPlayer(args[1]);
        if (target == null) {
            player.sendMessage(ChatColor.RED + "Игрок не найден!");
            return;
        }
        
        // Удаляем игрока
        if (plugin.getPrivateManager().removePlayerFromPrivate(region.getId(), target.getUniqueId(), player.getUniqueId())) {
            player.sendMessage(ChatColor.GREEN + "Игрок " + target.getName() + " удален из привата!");
            target.sendMessage(ChatColor.YELLOW + "Вы удалены из привата игрока " + player.getName() + "!");
        } else {
            player.sendMessage(ChatColor.RED + "Не удалось удалить игрока из привата!");
        }
    }
    
    private void handlePvPCommand(Player player) {
        // Находим приват в текущей локации
        PrivateRegion region = plugin.getPrivateManager().getPrivateByLocation(player.getLocation());
        if (region == null) {
            player.sendMessage(ChatColor.RED + "Вы не находитесь в привате!");
            return;
        }
        
        // Проверяем, что игрок - владелец
        if (!region.getOwner().equals(player.getUniqueId())) {
            player.sendMessage(ChatColor.RED + "Вы не являетесь владельцем этого привата!");
            return;
        }
        
        // Переключаем PvP
        if (plugin.getPrivateManager().togglePvP(region.getId(), player.getUniqueId())) {
            String status = region.isPvpEnabled() ? ChatColor.RED + "включен" : ChatColor.GREEN + "выключен";
            player.sendMessage(ChatColor.YELLOW + "PvP в привате " + status + ChatColor.YELLOW + "!");
        } else {
            player.sendMessage(ChatColor.RED + "Не удалось изменить настройки PvP!");
        }
    }
    
    private void handleInfoCommand(Player player) {
        // Находим приват в текущей локации
        PrivateRegion region = plugin.getPrivateManager().getPrivateByLocation(player.getLocation());
        if (region == null) {
            player.sendMessage(ChatColor.RED + "Вы не находитесь в привате!");
            return;
        }
        
        // Получаем информацию о владельце
        String ownerName = Bukkit.getOfflinePlayer(region.getOwner()).getName();
        if (ownerName == null) ownerName = "Неизвестно";
        
        // Формируем информацию
        player.sendMessage(ChatColor.GOLD + "=== Информация о привате ===");
        player.sendMessage(ChatColor.YELLOW + "ID: " + ChatColor.WHITE + region.getId());
        player.sendMessage(ChatColor.YELLOW + "Владелец: " + ChatColor.WHITE + ownerName);
        player.sendMessage(ChatColor.YELLOW + "Мир: " + ChatColor.WHITE + region.getWorld().getName());
        
        Location loc = region.getBlockLocation();
        player.sendMessage(ChatColor.YELLOW + "Координаты: " + ChatColor.WHITE + 
                          String.format("X: %d, Y: %d, Z: %d", loc.getBlockX(), loc.getBlockY(), loc.getBlockZ()));
        
        player.sendMessage(ChatColor.YELLOW + "Тип блока: " + ChatColor.WHITE + region.getBlockType().name());
        player.sendMessage(ChatColor.YELLOW + "PvP: " + 
                          (region.isPvpEnabled() ? ChatColor.RED + "Включен" : ChatColor.GREEN + "Выключен"));
        
        player.sendMessage(ChatColor.YELLOW + "Участники (" + region.getMembers().size() + "): ");
        for (java.util.UUID memberId : region.getMembers()) {
            String memberName = Bukkit.getOfflinePlayer(memberId).getName();
            if (memberName != null) {
                player.sendMessage(ChatColor.WHITE + "- " + memberName);
            }
        }
        
        player.sendMessage(ChatColor.YELLOW + "Создан: " + ChatColor.WHITE + 
                          dateFormat.format(new Date(region.getCreatedTime())));
    }
    
    private void handleListCommand(Player player) {
        List<PrivateRegion> playerPrivates = plugin.getPrivateManager().getPlayerPrivates(player.getUniqueId());
        
        if (playerPrivates.isEmpty()) {
            player.sendMessage(ChatColor.YELLOW + "У вас нет приватов!");
            return;
        }
        
        player.sendMessage(ChatColor.GOLD + "=== Ваши приваты (" + playerPrivates.size() + ") ===");
        
        for (int i = 0; i < playerPrivates.size(); i++) {
            PrivateRegion region = playerPrivates.get(i);
            Location loc = region.getBlockLocation();
            
            player.sendMessage(ChatColor.YELLOW + "" + (i + 1) + ". " + ChatColor.WHITE + region.getId());
            player.sendMessage(ChatColor.GRAY + "   Мир: " + region.getWorld().getName() + 
                              " | Координаты: " + loc.getBlockX() + ", " + loc.getBlockY() + ", " + loc.getBlockZ());
            player.sendMessage(ChatColor.GRAY + "   Участники: " + region.getMembers().size() + 
                              " | PvP: " + (region.isPvpEnabled() ? "Вкл" : "Выкл"));
        }
    }
    
    private void sendHelp(Player player) {
        player.sendMessage(ChatColor.GOLD + "=== Команды приватов ===");
        player.sendMessage(ChatColor.YELLOW + "/private add <игрок>" + ChatColor.WHITE + " - Добавить игрока в приват");
        player.sendMessage(ChatColor.YELLOW + "/private remove <игрок>" + ChatColor.WHITE + " - Удалить игрока из привата");
        player.sendMessage(ChatColor.YELLOW + "/private pvp" + ChatColor.WHITE + " - Переключить PvP в привате");
        player.sendMessage(ChatColor.YELLOW + "/private info" + ChatColor.WHITE + " - Информация о текущем привате");
        player.sendMessage(ChatColor.YELLOW + "/private list" + ChatColor.WHITE + " - Список ваших приватов");
        player.sendMessage(ChatColor.GRAY + "Для создания привата поставьте алмазный/изумрудный/золотой/железный блок или маяк");
    }
}
