# Конфигурация плагина RegionPrivates

# Настройки приватов
private:
  # Радиус защиты вокруг блока-привата (в блоках)
  protection-radius: 16
  
  # Минимальное расстояние между приватами (в блоках)
  min-distance: 32
  
  # Максимальное количество приватов на игрока (0 = без ограничений)
  max-privates-per-player: 5
  
  # Разрешенные блоки для создания приватов
  allowed-blocks:
    - DIAMOND_BLOCK
    - EMERALD_BLOCK
    - GOLD_BLOCK
    - IRON_BLOCK
    - BEACON

# Настройки защиты
protection:
  # Защита от взрывов
  prevent-explosions: true
  
  # Защита от огня
  prevent-fire: true
  
  # Защита от потоков жидкости
  prevent-liquid-flow: true
  
  # Защита от поршней
  prevent-pistons: true
  
  # Предотвращение спавна враждебных мобов
  prevent-monster-spawn: true

# Сообщения плагина
messages:
  prefix: "&6[RegionPrivates]&r "
  
  # Сообщения об успехе
  private-created: "&aПриват успешно создан!"
  private-removed: "&eПриват удален!"
  player-added: "&aИгрок {player} добавлен в приват!"
  player-removed: "&eИгрок {player} удален из привата!"
  pvp-enabled: "&cPvP в привате включен!"
  pvp-disabled: "&aPvP в привате выключен!"
  
  # Сообщения об ошибках
  no-permission: "&cУ вас нет прав на это действие!"
  not-in-private: "&cВы не находитесь в привате!"
  not-owner: "&cВы не являетесь владельцем этого привата!"
  player-not-found: "&cИгрок не найден!"
  too-close: "&cСлишком близко к другому привату! Минимальное расстояние: {distance} блоков"
  max-privates-reached: "&cВы достигли максимального количества приватов ({max})!"
  cannot-break-private: "&cВы не можете сломать чужой приват!"
  cannot-interact: "&cВы не можете взаимодействовать с блоками в чужом привате!"
  pvp-disabled-here: "&cPvP запрещен в этом привате!"
  
  # Информационные сообщения
  protection-zone: "&eЗона защиты: {radius} блоков во все стороны от блока"
  use-commands: "&eИспользуйте /private для управления приватом"
  private-owner: "&cВладелец привата: {owner}"
